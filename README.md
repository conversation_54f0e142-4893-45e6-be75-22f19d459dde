# Instagram UI - Material You Design

Material You (Material Design 3) tasarım sistemi kullanılarak oluşturulmuş modern Instagram anasayfa klonu.

## Özellikler

- ✨ **Material You Design**: Modern Material Design 3 bileşenleri
- 🎨 **Dinamik Renkler**: Sistem temasına uyumlu renk paleti
- 📱 **Responsive Tasarım**: Farklı ekran boyutlarına uyumlu
- 🌙 **Karanlık/Açık Tema**: Sistem teması desteği
- 📖 **Story Bölümü**: Yatay kaydırmalı hikaye listesi
- 📝 **Post Feed**: Instagram benzeri gönderi akışı
- ❤️ **Etkileşim**: Beğeni, yorum, paylaşım butonları
- 🔖 **Kaydetme**: Gönderileri kaydetme özelliği
- 🔄 **Pull to Refresh**: Aşağı çekerek yenileme

## Ekran Görüntüleri

### Ana Sayfa
- Instagram benzeri üst bar
- Story listesi (yatay scroll)
- Post feed (dikey scroll)
- Alt navigasyon çubuğu

### Özellikler
- **Stories**: Profil fotoğrafları, canlı yayın göstergeleri, görüntülenme durumu
- **Posts**: Profil bilgileri, gönderi görselleri, beğeni/yorum sayıları
- **Etkileşim**: Animasyonlu beğeni butonu, kaydetme özelliği

## Teknolojiler

- **Flutter**: Cross-platform mobil uygulama framework'ü
- **Material Design 3**: Google'ın en yeni tasarım sistemi
- **Dart**: Programlama dili

## Kurulum

1. Flutter SDK'nın kurulu olduğundan emin olun
2. Projeyi klonlayın veya indirin
3. Bağımlılıkları yükleyin:
   ```bash
   flutter pub get
   ```
4. Uygulamayı çalıştırın:
   ```bash
   flutter run
   ```

## Proje Yapısı

```
lib/
├── main.dart                 # Ana uygulama dosyası
├── screens/
│   └── home_screen.dart      # Ana sayfa ekranı
├── widgets/
│   ├── story_widget.dart     # Story bileşeni
│   └── post_widget.dart      # Post bileşeni
└── models/
    ├── story_model.dart      # Story veri modeli
    └── post_model.dart       # Post veri modeli
```

## Özelleştirme

### Renk Teması
`main.dart` dosyasında `seedColor` değerini değiştirerek ana renk temasını özelleştirebilirsiniz:

```dart
colorScheme: ColorScheme.fromSeed(
  seedColor: const Color(0xFFE4405F), // Instagram pembe
  brightness: Brightness.light,
),
```

### Örnek Veriler
`models/` klasöründeki dosyalarda örnek story ve post verilerini düzenleyebilirsiniz.

## Geliştirme Notları

- Material Design 3 bileşenleri kullanılmıştır
- Responsive tasarım için MediaQuery kullanılmıştır
- State management için StatefulWidget kullanılmıştır
- Animasyonlar için AnimatedSwitcher kullanılmıştır

## Lisans

Bu proje eğitim amaçlı oluşturulmuştur.
