<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram UI - Material You Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fafafa;
            color: #262626;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid #dbdbdb;
            background: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #262626;
        }

        .header-icons {
            display: flex;
            gap: 16px;
        }

        .icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
        }

        .stories {
            display: flex;
            padding: 16px;
            gap: 12px;
            overflow-x: auto;
            border-bottom: 1px solid #dbdbdb;
        }

        .story {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 70px;
            cursor: pointer;
        }

        .story-avatar {
            width: 66px;
            height: 66px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 4px;
        }

        .story-avatar.viewed {
            background: #c7c7c7;
        }

        .story-avatar.own {
            background: #dbdbdb;
            border: 2px solid #dbdbdb;
        }

        .story-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #e1306c;
            border: 3px solid white;
        }

        .story-username {
            font-size: 12px;
            text-align: center;
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .post {
            margin-bottom: 24px;
            background: white;
        }

        .post-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
        }

        .post-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e1306c;
            margin-right: 12px;
        }

        .post-username {
            font-weight: 600;
            flex: 1;
        }

        .post-image {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .post-actions {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            gap: 16px;
        }

        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 24px;
        }

        .like-btn.liked {
            color: #ed4956;
        }

        .post-likes {
            font-weight: 600;
            padding: 0 16px;
            margin-bottom: 8px;
        }

        .post-caption {
            padding: 0 16px;
            margin-bottom: 8px;
        }

        .post-time {
            padding: 0 16px;
            color: #8e8e8e;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            background: white;
            border-top: 1px solid #dbdbdb;
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #262626;
            font-size: 12px;
        }

        .nav-item.active {
            color: #e1306c;
        }

        .live-badge {
            background: #ff3040;
            color: white;
            font-size: 8px;
            padding: 2px 4px;
            border-radius: 3px;
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
        }

        @media (prefers-color-scheme: dark) {
            body {
                background: #000;
                color: #fff;
            }
            
            .container {
                background: #000;
            }
            
            .header, .post, .bottom-nav {
                background: #000;
                border-color: #262626;
            }
            
            .stories {
                border-color: #262626;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">Instagram</div>
            <div class="header-icons">
                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                </svg>
            </div>
        </div>

        <!-- Stories -->
        <div class="stories">
            <div class="story">
                <div class="story-avatar own">
                    <div class="story-image"></div>
                </div>
                <div class="story-username">Hikayeniz</div>
            </div>
            <div class="story">
                <div class="story-avatar">
                    <div class="story-image"></div>
                </div>
                <div class="story-username">ahmet_yilmaz</div>
                <div class="live-badge">CANLI</div>
            </div>
            <div class="story">
                <div class="story-avatar viewed">
                    <div class="story-image"></div>
                </div>
                <div class="story-username">elif_kaya</div>
            </div>
            <div class="story">
                <div class="story-avatar">
                    <div class="story-image"></div>
                </div>
                <div class="story-username">mehmet_demir</div>
            </div>
            <div class="story">
                <div class="story-avatar">
                    <div class="story-image"></div>
                </div>
                <div class="story-username">ayse_ozkan</div>
            </div>
        </div>

        <!-- Posts -->
        <div class="posts">
            <div class="post">
                <div class="post-header">
                    <div class="post-avatar"></div>
                    <div class="post-username">ahmet_yilmaz</div>
                    <button class="action-btn">⋯</button>
                </div>
                <div class="post-image">📸 Güzel bir gün!</div>
                <div class="post-actions">
                    <button class="action-btn like-btn" onclick="toggleLike(this)">♡</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                    <div style="margin-left: auto;">
                        <button class="action-btn bookmark-btn" onclick="toggleBookmark(this)">🔖</button>
                    </div>
                </div>
                <div class="post-likes">142 beğenme</div>
                <div class="post-caption">
                    <strong>ahmet_yilmaz</strong> Güzel bir gün! ☀️ #güneş #mutluluk #hayat
                </div>
                <div class="post-time">2 saat önce</div>
            </div>

            <div class="post">
                <div class="post-header">
                    <div class="post-avatar"></div>
                    <div class="post-username">elif_kaya</div>
                    <button class="action-btn">⋯</button>
                </div>
                <div class="post-image">🌲 Doğa yürüyüşü</div>
                <div class="post-actions">
                    <button class="action-btn like-btn liked" onclick="toggleLike(this)">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                    <div style="margin-left: auto;">
                        <button class="action-btn bookmark-btn" onclick="toggleBookmark(this)">🔖</button>
                    </div>
                </div>
                <div class="post-likes">89 beğenme</div>
                <div class="post-caption">
                    <strong>elif_kaya</strong> Doğa yürüyüşü 🌲🥾 #doğa #yürüyüş #huzur
                </div>
                <div class="post-time">4 saat önce</div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="#" class="nav-item active">
                <span style="font-size: 20px;">🏠</span>
                <span>Ana Sayfa</span>
            </a>
            <a href="#" class="nav-item">
                <span style="font-size: 20px;">🔍</span>
                <span>Keşfet</span>
            </a>
            <a href="#" class="nav-item">
                <span style="font-size: 20px;">🎬</span>
                <span>Reels</span>
            </a>
            <a href="#" class="nav-item">
                <span style="font-size: 20px;">🛍️</span>
                <span>Shop</span>
            </a>
            <a href="#" class="nav-item">
                <span style="font-size: 20px;">👤</span>
                <span>Profil</span>
            </a>
        </div>
    </div>

    <script>
        function toggleLike(btn) {
            if (btn.classList.contains('liked')) {
                btn.classList.remove('liked');
                btn.textContent = '♡';
            } else {
                btn.classList.add('liked');
                btn.textContent = '❤️';
            }
        }

        function toggleBookmark(btn) {
            if (btn.textContent === '🔖') {
                btn.textContent = '📌';
            } else {
                btn.textContent = '🔖';
            }
        }

        // Add some interactivity
        document.querySelectorAll('.story').forEach(story => {
            story.addEventListener('click', () => {
                alert('Story açılıyor: ' + story.querySelector('.story-username').textContent);
            });
        });
    </script>
</body>
</html>
