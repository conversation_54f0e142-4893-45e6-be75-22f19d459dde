class Post {
  final String id;
  final String username;
  final String profileImage;
  final String postImage;
  final String caption;
  final int likes;
  final int comments;
  final String timeAgo;
  final bool isLiked;
  final bool isBookmarked;
  final List<String> tags;

  Post({
    required this.id,
    required this.username,
    required this.profileImage,
    required this.postImage,
    required this.caption,
    required this.likes,
    required this.comments,
    required this.timeAgo,
    this.isLiked = false,
    this.isBookmarked = false,
    this.tags = const [],
  });

  static List<Post> getSamplePosts() {
    return [
      Post(
        id: '1',
        username: 'ahmet_yil<PERSON><PERSON>',
        profileImage: 'assets/images/profile2.png',
        postImage: 'assets/images/post1.png',
        caption: 'Güzel bir gün! ☀️ #güneş #mutluluk #hayat',
        likes: 142,
        comments: 23,
        timeAgo: '2 saat önce',
        tags: ['güneş', 'mutluluk', 'hayat'],
      ),
      Post(
        id: '2',
        username: 'elif_kaya',
        profileImage: 'assets/images/profile3.png',
        postImage: 'assets/images/post2.png',
        caption: 'Do<PERSON><PERSON> yürüyüşü 🌲🥾 #doğa #yürüyüş #huzur',
        likes: 89,
        comments: 12,
        timeAgo: '4 saat önce',
        isLiked: true,
        tags: ['doğa', 'yürüyüş', 'huzur'],
      ),
      Post(
        id: '3',
        username: 'mehmet_demir',
        profileImage: 'assets/images/profile4.png',
        postImage: 'assets/images/post3.png',
        caption: 'Kahve molası ☕️ #kahve #mola #istanbul',
        likes: 67,
        comments: 8,
        timeAgo: '6 saat önce',
        isBookmarked: true,
        tags: ['kahve', 'mola', 'istanbul'],
      ),
      Post(
        id: '4',
        username: 'ayse_ozkan',
        profileImage: 'assets/images/profile5.png',
        postImage: 'assets/images/post4.png',
        caption: 'Yeni kitap önerisi 📚 #kitap #okuma #edebiyat',
        likes: 234,
        comments: 45,
        timeAgo: '8 saat önce',
        isLiked: true,
        tags: ['kitap', 'okuma', 'edebiyat'],
      ),
      Post(
        id: '5',
        username: 'can_arslan',
        profileImage: 'assets/images/profile6.png',
        postImage: 'assets/images/post5.png',
        caption: 'Spor zamanı! 💪 #spor #sağlık #motivasyon',
        likes: 156,
        comments: 31,
        timeAgo: '12 saat önce',
        tags: ['spor', 'sağlık', 'motivasyon'],
      ),
    ];
  }
}
