class Story {
  final String id;
  final String username;
  final String profileImage;
  final bool isViewed;
  final bool isLive;
  final bool isOwn;

  Story({
    required this.id,
    required this.username,
    required this.profileImage,
    this.isViewed = false,
    this.isLive = false,
    this.isOwn = false,
  });

  static List<Story> getSampleStories() {
    return [
      Story(
        id: '1',
        username: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        profileImage: 'assets/images/profile1.png',
        isOwn: true,
      ),
      <PERSON>(
        id: '2',
        username: 'ahmet_yil<PERSON><PERSON>',
        profileImage: 'assets/images/profile2.png',
        isLive: true,
      ),
      <PERSON>(
        id: '3',
        username: 'elif_kaya',
        profileImage: 'assets/images/profile3.png',
        isViewed: true,
      ),
      <PERSON>(
        id: '4',
        username: 'mehmet_demir',
        profileImage: 'assets/images/profile4.png',
      ),
      <PERSON>(
        id: '5',
        username: 'ayse_ozkan',
        profileImage: 'assets/images/profile5.png',
      ),
      <PERSON>(
        id: '6',
        username: 'can_a<PERSON><PERSON>',
        profileImage: 'assets/images/profile6.png',
        isViewed: true,
      ),
      <PERSON>(
        id: '7',
        username: 'zey<PERSON><PERSON>_kurt',
        profileImage: 'assets/images/profile7.png',
      ),
      Story(
        id: '8',
        username: 'emre_sahin',
        profileImage: 'assets/images/profile8.png',
      ),
    ];
  }
}
