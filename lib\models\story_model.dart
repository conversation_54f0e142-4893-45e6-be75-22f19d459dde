class Story {
  final String id;
  final String username;
  final String profileImage;
  final bool isViewed;
  final bool isLive;
  final bool isOwn;

  Story({
    required this.id,
    required this.username,
    required this.profileImage,
    this.isViewed = false,
    this.isLive = false,
    this.isOwn = false,
  });

  static List<Story> getSampleStories() {
    return [
      Story(
        id: '1',
        username: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        profileImage: 'https://via.placeholder.com/150',
        isOwn: true,
      ),
      <PERSON>(
        id: '2',
        username: 'ahmet_yil<PERSON><PERSON>',
        profileImage: 'https://via.placeholder.com/150/FF5733',
        isLive: true,
      ),
      <PERSON>(
        id: '3',
        username: 'elif_kaya',
        profileImage: 'https://via.placeholder.com/150/33FF57',
        isViewed: true,
      ),
      <PERSON>(
        id: '4',
        username: 'mehmet_demir',
        profileImage: 'https://via.placeholder.com/150/3357FF',
      ),
      <PERSON>(
        id: '5',
        username: 'ayse_ozkan',
        profileImage: 'https://via.placeholder.com/150/FF33F5',
      ),
      <PERSON>(
        id: '6',
        username: 'can_arslan',
        profileImage: 'https://via.placeholder.com/150/F5FF33',
        isViewed: true,
      ),
      Story(
        id: '7',
        username: 'zeynep_kurt',
        profileImage: 'https://via.placeholder.com/150/33FFF5',
      ),
      Story(
        id: '8',
        username: 'emre_sahin',
        profileImage: 'https://via.placeholder.com/150/FF8C33',
      ),
    ];
  }
}
