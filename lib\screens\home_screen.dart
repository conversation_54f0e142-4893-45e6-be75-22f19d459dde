import 'package:flutter/material.dart';
import '../models/story_model.dart';
import '../models/post_model.dart';
import '../widgets/story_widget.dart';
import '../widgets/post_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<Story> stories = Story.getSampleStories();
  final List<Post> posts = Post.getSamplePosts();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(
              'Instagram',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.favorite_border),
            ),
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.chat_bubble_outline),
            ),
          ],
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Simulate refresh
          await Future.delayed(const Duration(seconds: 1));
        },
        child: CustomScrollView(
          slivers: [
            // Stories section
            SliverToBoxAdapter(
              child: Container(
                height: 110,
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  itemCount: stories.length,
                  itemBuilder: (context, index) {
                    return StoryWidget(
                      story: stories[index],
                      onTap: () {
                        // Handle story tap
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${stories[index].username} hikayesi'),
                            duration: const Duration(seconds: 1),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
            
            // Divider
            SliverToBoxAdapter(
              child: Divider(
                height: 1,
                thickness: 0.5,
                color: colorScheme.outline.withOpacity(0.3),
              ),
            ),
            
            // Posts section
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  return PostWidget(post: posts[index]);
                },
                childCount: posts.length,
              ),
            ),
            
            // Bottom padding
            const SliverToBoxAdapter(
              child: SizedBox(height: 80),
            ),
          ],
        ),
      ),
    );
  }
}
