import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:instagram_ui/main.dart';

void main() {
  group('Instagram UI Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const InstagramApp());

      // Verify that the app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Main screen should have bottom navigation', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());

      // Verify that NavigationBar exists
      expect(find.byType(NavigationBar), findsOneWidget);
      
      // Verify navigation items
      expect(find.text('Ana Sayfa'), findsOneWidget);
      expect(find.text('Keşfet'), findsOneWidget);
      expect(find.text('Reels'), findsOneWidget);
      expect(find.text('Shop'), findsOneWidget);
      expect(find.text('Profil'), findsOneWidget);
    });

    testWidgets('Home screen should display Instagram title', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());

      // Verify Instagram title in AppBar
      expect(find.text('Instagram'), findsOneWidget);
    });

    testWidgets('Home screen should display stories', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());
      await tester.pumpAndSettle();

      // Verify that stories are displayed
      expect(find.text('Hikayeniz'), findsOneWidget);
      expect(find.text('ahmet_yilmaz'), findsAtLeastNWidgets(1));
      expect(find.text('elif_kaya'), findsAtLeastNWidgets(1));
    });

    testWidgets('Navigation should work', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());

      // Tap on Keşfet tab
      await tester.tap(find.text('Keşfet'));
      await tester.pumpAndSettle();

      // Verify that we're on the search screen
      expect(find.text('Keşfet Sayfası'), findsOneWidget);

      // Go back to home
      await tester.tap(find.text('Ana Sayfa'));
      await tester.pumpAndSettle();

      // Verify we're back on home screen
      expect(find.text('Instagram'), findsOneWidget);
    });

    testWidgets('Posts should be displayed', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());
      await tester.pumpAndSettle();

      // Verify that posts are displayed
      expect(find.text('ahmet_yilmaz'), findsAtLeastNWidgets(1));
      expect(find.text('elif_kaya'), findsAtLeastNWidgets(1));

      // Verify post captions are displayed
      expect(find.textContaining('beğenme'), findsAtLeastNWidgets(1));
      expect(find.textContaining('saat önce'), findsAtLeastNWidgets(1));
    });

    testWidgets('Like button should work', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());
      await tester.pumpAndSettle();

      // Scroll down to make sure like buttons are visible
      await tester.drag(find.byType(CustomScrollView), const Offset(0, -300));
      await tester.pumpAndSettle();

      // Find and tap like button
      final likeButtons = find.byIcon(Icons.favorite_border);
      if (likeButtons.evaluate().isNotEmpty) {
        await tester.tap(likeButtons.first, warnIfMissed: false);
        await tester.pumpAndSettle();

        // Verify like button changed to filled heart
        expect(find.byIcon(Icons.favorite), findsAtLeastNWidgets(1));
      }
    });

    testWidgets('Story tap should show snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());
      await tester.pumpAndSettle();

      // Find and tap a story
      final storyWidget = find.text('ahmet_yilmaz').first;
      await tester.tap(storyWidget);
      await tester.pumpAndSettle();

      // Verify snackbar appears
      expect(find.textContaining('hikayesi'), findsOneWidget);
    });

    testWidgets('Material You theme should be applied', (WidgetTester tester) async {
      await tester.pumpWidget(const InstagramApp());

      // Get the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Verify Material 3 is enabled
      expect(materialApp.theme?.useMaterial3, isTrue);
      
      // Verify seed color is Instagram pink
      expect(materialApp.theme?.colorScheme.primary, isNotNull);
    });
  });
}
